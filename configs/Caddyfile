api.appio.so {
        root * /var/www/api.appio.so

        # Port configured in configs/config.toml
        # Also handling error pages
        handle {
            reverse_proxy localhost:8082
        }

        encode zstd gzip
        header -Server # Remove the Server header

        # SSL enabled in :443 {}}
        # tls /etc/caddy/certs/cert.pem /etc/caddy/certs/key.pem
        log {
                output file /var/log/caddy/api.appio.so-access.log
        }
        log {
                level ERROR
                output file /var/log/caddy/api.appio.so-error.log
        }
}